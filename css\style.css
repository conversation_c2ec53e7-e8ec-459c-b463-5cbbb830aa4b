*{
    margin: 0;
    padding: 0;
    box-sizing:border-box ;
}
body{
    font-family: sans-serif;
    height: 5000px;
}
.header{
    width: 100%;
    height: 70px;
    background-color:#0B9586 ;
}
.header img{
    float: left;
    width: 70px;
    height: 40px;
    margin: 15px 10px 5px 100px;
    mix-blend-mode: multiply;
}
.list {
    list-style: none; 
    float: right; 
    margin-right: 10%;
}
.list li{
    display: inline-block;
    padding:10px ;
}
.list li .call{
    margin-left: 90px;
}
.list li a {
    text-decoration: none;
    font-style: Inter, sans-serif;
    font-size: large;
    color: white;
    font-weight: 500;
    margin-right: 25px;
}
.list li p{
    font-weight: 500;
    color: white;
    margin-left: 80px;
}
.list li .hover{
   text-shadow: #101828;
}
.section1{
    padding-top: 100px;
    background-image: url(../images/solar-panels-meadow\ 1.png);
    background-repeat: no-repeat;
    background-size: auto;
    background-position: top;
    background-position-x: center;
    height: 120vh;
}
 .progress-content{
    margin-left: 150px;
    /* height: 10px important; */
    display: flex;
    position: absolute;
 }
 .progress{
    display: flex;
    width: 200px;
    height: 10px ; 
    border-radius: 80px;
    background-color: rgb(67, 67, 67);
    
    margin: 20px 5px;
   /* mix-blend-mode: multiply; */
    
    position: relative;
 }
 .progress::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100%;
    border-radius: 80px;
    background-color: white;

 }
.section1 .frame13{
    width: 730px;
    height: 305px;
    margin-top: 80px;
    margin-left: 150px;
    color: white;
    
}
.frame13 .text{
    font-size: 52px;
    font-family: Inter,sans-serif;
    font-weight: 600;
    margin-bottom: 20px; 
}
.section1 .p1{
    font-family:sans-serif ;
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 30px;
}
.section1 button{
    width: 200px;
    height: 40px;
    border-radius:30px ;
    padding: 8px;
}
.btn1{
    border-style: none;
    background-color: #3CAB90;
    color: white;
}
.btn2{
    border-style: none;
    background-color: #FFBB1C;
    margin-left: 20px;
   
}
/* //////////////////////// */
.section2{
    width: 85%;
    height: 690px;
    padding-top: 30px;
    margin: auto;
}
.text2{
    width: 540px;
    height:476px ;
}
.text2 h2{
   font-size: 42px;
   font-weight: 600px;
   line-height: 51px;
}
.text2 p{
    font-size: 16px;
    font-weight:400 ;
    line-height: 24px;
   margin-top: 20px;
}
.img{
    float: right;
    width: 530px;
    height: 552px;
   margin-top: -470px;
}
.img img{
    width:469px ;
    height:552px ;
    border-radius:12px ;
    left: 50px;
    float: right;

  
}
.over{
    background-color:#FFBB1C ;
    width: 266px;
    height: 208px;
    border-radius:12px ;
    padding: 24px  30px  24px  30px;
     z-index: 10; 
    margin-left: 545px;
    position: relative; 
    margin-top: -150px;
}
 .over p{
    font-size:18px ;
    font-family: sans-serif;
    margin-top: 20px; 
    position: absolute;  
   
} 
.frame56{
    width: 550px;
    height: 155px;   
    margin-top: -220px;
    margin-left: 25px;
   
}
.frame56 ul li{
    font-size: large;
    font-family: sans-serif;
    font-weight: 600;
    line-height: 2.9;
    display: flex;
}
.section3{
    background-image: url(../images/8116\ 1.png);
    background-repeat: no-repeat;
    height: 85vh;
}
.section3 .frame66{
    width: 730px;
    height: 215px;
    padding: 150px 10px 10px  130px;
}
.frame66 h2{
    font-size: 40px;
    font-family: initial;
    font-weight: 600;
    line-height:50px ;
    color: white;
}
.frame66 p{
    font-family: Inter;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: white;
    margin-top: 20px;
    margin-bottom: 25px;
}
.frame66 button{
    border-width: 0;
    background-color: #FFBB1C;
    height: 41px;
    padding: 12px 34px 12px 34px;
    gap: 10px;
    border-radius: 109px;
}
/* /////////////////////// */
 .ground{
    background-color: rgb(246, 244, 244);
    height: 130vh;
} 
.section4{
    width: 80%;
    /* height: 780px; */
    margin: auto;
    display: flex;
    justify-content: space-between;
}
.head-sec4{
    width: 800px;
    margin: auto;
}
.solar-header{
    text-align: center;
    padding: 40px 20px 40px 20px;
    font-weight: 600;
    font-size: 42px;
    line-height: 52px;
}
.psection4{
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    line-height: 24px;

}
.div41,.div42,.div43{
    width: 30%;
    margin-top: 50px;
    background-color: white;
}
.h2-sec4{
    margin: 15px 5px 20px 25px;
}
.p-sec4{
    margin: 0px 10px 10px 10px;
    color: #475467;
}
.tail{
    display: flex;
    margin: 0px 5px 20px 0px;
    color: #3CAB90;
}
.tail h2 {
    margin-left: 10px;
    margin-right: 8px;
    font-weight: 500;
}
.tail i{
   margin-top: 6px;
}
/* //section5// */
.sec5{
    background-image: url(../images/background\ sec5.png);
    background-size: cover;
    height: 80vh;
    display: flex;
    /* justify-content: center;
    align-items: center; */

}
.section5{
    margin: auto;
    width: 745px;
    height: 300px;
    background-color: #00000091;
    border-radius: 12px;
    color: rgb(255, 255, 255);
    padding: 30px 20px;
    backdrop-filter: blur(24px);
    display: flex;
    flex-direction: column;
    text-align: center;
}
.text-sec5{
    font-size: 18px;
    line-height: 28px;
    text-align: center;
    font-weight: 500;
    width: 650px;
    padding: 10px;
    margin: auto;
}

/* section 6 */
.section6{
    background-color:#FFBB1C0D ;
    margin-top: -85px;
}
.sec6{
    width: 80vw;
    margin: auto;
    display: flex;
    /* margin-top: 100px; */
    /* background-color: #FFBB1C0D; */
    padding-top: 200px;
    padding-bottom: 100px;
}
.div61{
    height: fit-content;
    width: 33%;
}
.div61 .icon{
    width: 50px;
    height: 50px;
    background-color: #3CAB90;
    border-radius: 10px;
    /* border: #3CAB9026 21px ; */
}
.icon img{
    padding: 12px;
}
.head1-sec6{
    font-size: 42px;
    font-weight: 600;
    line-height: 51px;
    margin-top: 10px;
}
.div61 p{
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    color: #475467;
}
.div62{
    width: 33%;
    margin: 0px 34px 0px 34px;
}
.block1 ,.block2 , .block3 , .block4{
    border-color: #3CAB90;
    border-radius: 12px;
    border-style: solid;
    padding: 24px 0px 0px 0px ;
    gap: 32px;
    border: 1px 0px 0px 0px ;
    background-color: #3CAB900D;
}
.block1 , .block3{
    margin-bottom: 25px;
}
.div63{
    width: 33%;
}
.img-sec6{
   width: 90%;
   margin: auto;
   height: 80px;
   
}
.img-sec6{
   padding: 15px 15px 10px 30px;
   background-color: #3CAB9026  ;
   border-radius: 10px;
   gap: 10px;
}
.img-sec6 img{
    width: 225px;
    height: 50px;
    mix-blend-mode: luminosity;
}
.div62 h3 , .div63 h3{
    margin-top: 15px;
    font-size: 20px;
    font-weight: 600;
    line-height: 25px;
    text-align: left;
    padding: 15px;
}
.div62 .p-sec6 , .div63 .p-sec6{
    color: #475467;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    padding: 15px;
}
span {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    text-align: left;
    color: #3CAB90;
    display: flex;
    padding: 15px;
}
span i{
    margin: 5px 0px 0px 15px;
}
/* section7 */
.section7{
    width: 80%;
    height: 125vh;
    margin: auto;
    /* margin-top: 80px; */
}
.head-sec7{
    text-align: center;
    padding: 50px 0px;
    line-height: 50px;
}
.head-sec7 h2{
    font-size: 40px;
    font-weight: 600;
    color: #101828;
}
.head-sec7 p{
    color: #475467;
    font-size: 18px;
    font-weight: 400;
}
.contain-sec7{
    display: flex;
    gap: 32px;
}
.div71 , .div72 , .div73{
    height: 420px;
    border-radius: 12px  ;
    border-color: #3CAB90;
    border-style: solid;
    padding: 32px;
    background-color: #3CAB900D;
}
.div71 h3 , .div72 h3 , .div73 h3{
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    color: #101828;
}
.div71 p , .div72 p , .div73 p{
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #475467;
    margin-top: 25px;
}
/* footer */
.footer{
    background-color: #283646;
    /* width: 100vw; */
    height: 415px;
    padding: 80px 150px 80px 150px;
    gap: 55px;
}
.foot1{
    width: Fill (1,140px)px;
    height: Hug (126px)px;
    gap: 0px;
    /* justify: space-between; */
    opacity: 0px;
    display: flex;
    margin-bottom: 60px;
}
.f1{
    width: 60%;
}
.f1 img{
    width: 185px;
    height: 64px;
    mix-blend-mode: luminosity;
}
.f1 li{
    display: inline-block;
    margin: 50px 40px  20px  0px;
    color: white;
}
.f2{
   padding: 0px 1px 0px 1px;
   gap: 0px;
   /* justify: space-between; */
}
.f2 .email{
   border-radius: 38px;
   padding: 0px 0px 0px 20px;
   border: 1px 0px 0px 0px;
   border-color: #B3BDC7;
   background-color: #FFFFFF17 9%;
   display: flex;
   height: 48px;
   width: 406px;
}
.foot2 ul{
    color: white;
    list-style: none;
    display: flex;
    margin-top: 30px;
}
.foot2 li{
   margin: 0px 10px 0px 10px;
   color: #92989F;
}
.foot2 span{
    margin-top: -20px;
}







